defmodule Repobot.Repo.Migrations.MigrateContentToFileContents do
  use Ecto.Migration
  import Ecto.Query

  def up do
    # Step 1: Create shared FileContent records for source files that are associated with repository files
    execute """
    INSERT INTO file_contents (id, blob, source_file_id, repository_file_id, inserted_at, updated_at)
    SELECT 
      gen_random_uuid(),
      COALESCE(rf.content, sf.content),  -- Use repository file content if available, fallback to source file content
      sf.id,
      sf.repository_file_id,
      NOW(),
      NOW()
    FROM source_files sf
    INNER JOIN repository_files rf ON sf.repository_file_id = rf.id
    WHERE sf.repository_file_id IS NOT NULL
      AND (sf.content IS NOT NULL OR rf.content IS NOT NULL)
    """

    # Step 2: Create exclusive FileContent records for source files without repository file associations
    execute """
    INSERT INTO file_contents (id, blob, source_file_id, repository_file_id, inserted_at, updated_at)
    SELECT 
      gen_random_uuid(),
      sf.content,
      sf.id,
      NULL,
      NOW(),
      NOW()
    FROM source_files sf
    WHERE sf.repository_file_id IS NULL
      AND sf.content IS NOT NULL
    """

    # Step 3: Create exclusive FileContent records for repository files without associated source files
    execute """
    INSERT INTO file_contents (id, blob, source_file_id, repository_file_id, inserted_at, updated_at)
    SELECT 
      gen_random_uuid(),
      rf.content,
      NULL,
      rf.id,
      NOW(),
      NOW()
    FROM repository_files rf
    LEFT JOIN source_files sf ON sf.repository_file_id = rf.id
    WHERE sf.id IS NULL
      AND rf.content IS NOT NULL
    """

    # Step 4: Update source_files.file_content_id to reference the created FileContent records
    execute """
    UPDATE source_files 
    SET file_content_id = fc.id
    FROM file_contents fc
    WHERE fc.source_file_id = source_files.id
    """

    # Step 5: Update repository_files.file_content_id to reference the created FileContent records
    execute """
    UPDATE repository_files 
    SET file_content_id = fc.id
    FROM file_contents fc
    WHERE fc.repository_file_id = repository_files.id
    """
  end

  def down do
    # Step 1: Copy content back from FileContent to source_files
    execute """
    UPDATE source_files 
    SET content = fc.blob
    FROM file_contents fc
    WHERE source_files.file_content_id = fc.id
      AND fc.source_file_id = source_files.id
    """

    # Step 2: Copy content back from FileContent to repository_files (only for exclusive content)
    execute """
    UPDATE repository_files 
    SET content = fc.blob
    FROM file_contents fc
    WHERE repository_files.file_content_id = fc.id
      AND fc.repository_file_id = repository_files.id
      AND fc.source_file_id IS NULL
    """

    # Step 3: Clear file_content_id references
    execute "UPDATE source_files SET file_content_id = NULL"
    execute "UPDATE repository_files SET file_content_id = NULL"

    # Step 4: Delete all FileContent records
    execute "DELETE FROM file_contents"
  end
end
