# File Content Refactoring Task List

## Overview
This refactoring addresses the design flaw where file content is duplicated between `repository_files` and `source_files` tables. The solution introduces a `FileContent` schema that centralizes content storage and eliminates duplication.

## 1. Database Schema Changes

### 1.1 Create FileContent Schema and Migration
- Create `lib/repobot/file_content.ex` Ecto schema
  - Primary key: `:binary_id`
  - Field: `blob` (`:text` type using `Repobot.Base64Content` custom type)
  - Timestamps
  - Belongs to `:source_file` (optional, nullable)
  - Belongs to `:repository_file` (optional, nullable)
  - Validation: at least one of source_file_id or repository_file_id must be present

### 1.2 Create Database Migration
- Create migration to add `file_contents` table
- Add foreign key columns to existing tables:
  - Add `file_content_id` to `source_files` table (nullable, references `file_contents`)
  - Add `file_content_id` to `repository_files` table (nullable, references `file_contents`)
- Add indexes for foreign keys
- Add constraint to ensure <PERSON><PERSON>ontent belongs to at least one file

### 1.3 Data Migration Strategy
- Create data migration to populate `file_contents` table from existing data
- For source files imported from GitHub: create shared FileContent records
- For manually created source files: create exclusive FileContent records
- For repository files without associated source files: create exclusive FileContent records
- Update foreign key references in both tables

## 2. Schema Updates

### 2.1 Update SourceFile Schema
- Add `belongs_to :file_content, Repobot.FileContent`
- Remove `:content` field from schema
- Update `@derive` Jason.Encoder to exclude content field
- Add virtual field `:content` that delegates to `file_content.blob`
- Update changeset functions to handle file_content association

### 2.2 Update RepositoryFile Schema  
- Add `belongs_to :file_content, Repobot.FileContent`
- Remove `:content` field from schema
- Add virtual field `:content` that delegates to `file_content.blob`
- Update changeset functions to handle file_content association

### 2.3 Update FileContent Schema Associations
- Update FileContent to have proper associations with SourceFile and RepositoryFile
- Add validation logic for content sharing rules

## 3. Context Layer Updates

### 3.1 Update SourceFiles Context
- Modify `create_source_file/1` to create FileContent records
- Update `update_source_file/2` to handle FileContent updates
- Update `update_source_file_from_push/2` to handle shared content updates
- Modify `import_file_content/3` to use FileContent associations
- Update template rendering functions to use `file_content.blob`
- Update all content access patterns throughout the module

### 3.2 Update RepositoryFiles Context
- Modify `create_repository_file/1` to create FileContent records
- Update `update_repository_file/2` to handle FileContent updates
- Remove `update_associated_source_files/1` function (no longer needed)
- Update `fetch_file_content/2` to work with FileContent
- Update all content access patterns throughout the module

### 3.3 Update Source File Import Logic
- Modify import logic to create shared FileContent when importing from GitHub
- Ensure proper association between repository_file and source_file via shared FileContent
- Update import functions to handle content sharing correctly

## 4. Webhook Handler Updates

### 4.1 Update Push Event Handler
- Modify `fetch_and_update_source_file/5` to update shared FileContent
- Update `sync_changed_files/3` to work with FileContent records
- Simplify content synchronization logic (no more dual updates)
- Update file change processing to handle FileContent updates

### 4.2 Update Repository Sync Logic
- Update `sync_file/5` and related functions to use FileContent
- Modify template rendering to access content via FileContent association
- Update content comparison logic in sync operations

## 5. LiveView and UI Updates

### 5.1 Update Source File Show Component
- Modify content display to use `source_file.file_content.blob`
- Update diff functionality to work with FileContent
- Ensure template rendering works with new content access pattern

### 5.2 Update Folder Show Component  
- Update file content access in diff functionality
- Modify content display patterns to use FileContent associations

### 5.3 Update Source File Edit Component
- Update form handling to work with FileContent
- Ensure content editing creates/updates FileContent properly

## 6. Migration Cleanup

### 6.1 Remove Legacy Content Columns
- Create migration to remove `content` column from `source_files` table
- Create migration to remove `content` column from `repository_files` table
- Update any remaining database constraints

### 6.2 Update Indexes and Constraints
- Remove any indexes on the old content columns
- Add appropriate indexes for FileContent associations
- Ensure referential integrity constraints are in place

## 7. Testing Updates

### 7.1 Update Unit Tests
- Update SourceFiles context tests to work with FileContent
- Update RepositoryFiles context tests to work with FileContent
- Update schema tests for new associations

### 7.2 Update Integration Tests
- Update webhook handler tests to verify FileContent behavior
- Update LiveView tests to work with new content access patterns
- Update template rendering tests

### 7.3 Update Test Fixtures
- Update test factories to create FileContent records
- Ensure test data properly represents shared vs exclusive content scenarios

## 8. Documentation and Validation

### 8.1 Update Documentation
- Update schema documentation to reflect new content storage approach
- Document content sharing rules and when content is shared vs exclusive

### 8.2 Validation and Cleanup
- Run comprehensive tests to ensure no regressions
- Verify that content synchronization works correctly
- Validate that template rendering continues to work
- Ensure webhook processing handles content updates properly
