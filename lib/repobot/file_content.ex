defmodule Repobot.FileContent do
  @moduledoc """
  Schema for storing file content that can be shared between source files and repository files.
  
  This schema centralizes content storage to eliminate duplication between the 
  `source_files` and `repository_files` tables. Content can be:
  - Shared: when a source file is imported from a repository file
  - Exclusive: when content belongs to only one file (manual source files or 
    repository files without associated source files)
  """
  
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "file_contents" do
    field :blob, Repobot.Base64Content

    belongs_to :source_file, Repobot.SourceFile
    belongs_to :repository_file, Repobot.RepositoryFile

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(file_content, attrs \\ %{}) do
    file_content
    |> cast(attrs, [:blob, :source_file_id, :repository_file_id])
    |> validate_required([:blob])
    |> validate_at_least_one_file_association()
    |> foreign_key_constraint(:source_file_id)
    |> foreign_key_constraint(:repository_file_id)
  end

  @doc """
  Changeset for creating shared content between a source file and repository file.
  """
  def shared_changeset(file_content, attrs \\ %{}) do
    file_content
    |> cast(attrs, [:blob, :source_file_id, :repository_file_id])
    |> validate_required([:blob, :source_file_id, :repository_file_id])
    |> foreign_key_constraint(:source_file_id)
    |> foreign_key_constraint(:repository_file_id)
  end

  @doc """
  Changeset for creating exclusive content for a single file.
  """
  def exclusive_changeset(file_content, attrs \\ %{}) do
    file_content
    |> cast(attrs, [:blob, :source_file_id, :repository_file_id])
    |> validate_required([:blob])
    |> validate_exactly_one_file_association()
    |> foreign_key_constraint(:source_file_id)
    |> foreign_key_constraint(:repository_file_id)
  end

  defp validate_at_least_one_file_association(changeset) do
    source_file_id = get_field(changeset, :source_file_id)
    repository_file_id = get_field(changeset, :repository_file_id)

    if is_nil(source_file_id) and is_nil(repository_file_id) do
      add_error(changeset, :base, "must be associated with at least one source file or repository file")
    else
      changeset
    end
  end

  defp validate_exactly_one_file_association(changeset) do
    source_file_id = get_field(changeset, :source_file_id)
    repository_file_id = get_field(changeset, :repository_file_id)

    cond do
      is_nil(source_file_id) and is_nil(repository_file_id) ->
        add_error(changeset, :base, "must be associated with exactly one source file or repository file")
      
      not is_nil(source_file_id) and not is_nil(repository_file_id) ->
        add_error(changeset, :base, "cannot be associated with both source file and repository file for exclusive content")
      
      true ->
        changeset
    end
  end
end
